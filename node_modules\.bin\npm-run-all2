#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules/npm-run-all2/bin/npm-run-all/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules/npm-run-all2/bin/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules/npm-run-all2/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules/npm-run-all2/bin/npm-run-all/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules/npm-run-all2/bin/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules/npm-run-all2/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/npm-run-all2@7.0.2/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../npm-run-all2/bin/npm-run-all/index.js" "$@"
else
  exec node  "$basedir/../npm-run-all2/bin/npm-run-all/index.js" "$@"
fi
