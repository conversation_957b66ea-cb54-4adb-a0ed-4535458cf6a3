#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/vite@6.3.5_@types+node@22.1_1c7854b49f087c0c45f0f4db635ae458/node_modules/vite/bin/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/vite@6.3.5_@types+node@22.1_1c7854b49f087c0c45f0f4db635ae458/node_modules/vite/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/vite@6.3.5_@types+node@22.1_1c7854b49f087c0c45f0f4db635ae458/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/vite@6.3.5_@types+node@22.1_1c7854b49f087c0c45f0f4db635ae458/node_modules/vite/bin/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/vite@6.3.5_@types+node@22.1_1c7854b49f087c0c45f0f4db635ae458/node_modules/vite/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/vite@6.3.5_@types+node@22.1_1c7854b49f087c0c45f0f4db635ae458/node_modules:/mnt/d/workspace/Acoding/safety-web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
