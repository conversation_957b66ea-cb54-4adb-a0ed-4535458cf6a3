import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, Role, Permission } from '@/types'

// 模拟用户数据
const mockUser: User = {
  id: 'user_001',
  username: 'admin',
  name: '系统管理员',
  email: '<EMAIL>',
  phone: '13800138000',
  avatar: '',
  roles: [
    {
      id: 'role_001',
      name: '超级管理员',
      code: 'super_admin',
      description: '系统超级管理员，拥有所有权限',
      permissions: ['*']
    }
  ],
  permissions: [
    {
      id: 'perm_001',
      name: '全部权限',
      code: '*',
      resource: '*',
      action: '*'
    }
  ],
  status: 'active',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const refreshToken = ref<string>('')
  const isLoggedIn = ref(false)

  // 计算属性
  const userRoles = computed(() => user.value?.roles || [])
  const userPermissions = computed(() => user.value?.permissions || [])
  const isAdmin = computed(() => 
    userRoles.value.some(role => role.code === 'super_admin' || role.code === 'admin')
  )

  // 方法
  const login = async (credentials: { username: string; password: string }) => {
    try {
      // 模拟登录API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟登录验证
      if (credentials.username === 'admin' && credentials.password === 'admin123') {
        // 设置用户信息
        user.value = mockUser
        token.value = 'mock_access_token_' + Date.now()
        refreshToken.value = 'mock_refresh_token_' + Date.now()
        isLoggedIn.value = true
        
        // 存储到localStorage
        localStorage.setItem('user', JSON.stringify(user.value))
        localStorage.setItem('token', token.value)
        localStorage.setItem('refreshToken', refreshToken.value)
        
        return {
          user: user.value,
          token: token.value,
          refreshToken: refreshToken.value
        }
      } else {
        throw new Error('用户名或密码错误')
      }
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    try {
      // 模拟登出API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 清除状态
      user.value = null
      token.value = ''
      refreshToken.value = ''
      isLoggedIn.value = false
      
      // 清除localStorage
      localStorage.removeItem('user')
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
    } catch (error) {
      throw error
    }
  }

  const refreshAccessToken = async () => {
    try {
      // 模拟刷新token API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (refreshToken.value) {
        token.value = 'mock_access_token_' + Date.now()
        localStorage.setItem('token', token.value)
        return token.value
      } else {
        throw new Error('Refresh token not found')
      }
    } catch (error) {
      // 刷新失败，清除所有状态
      await logout()
      throw error
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    try {
      // 模拟更新用户资料API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (user.value) {
        user.value = { ...user.value, ...profileData }
        localStorage.setItem('user', JSON.stringify(user.value))
      }
    } catch (error) {
      throw error
    }
  }

  const changePassword = async (passwordData: {
    oldPassword: string
    newPassword: string
    confirmPassword: string
  }) => {
    try {
      // 模拟修改密码API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        throw new Error('新密码和确认密码不一致')
      }
      
      // 模拟验证旧密码
      if (passwordData.oldPassword !== 'admin123') {
        throw new Error('原密码错误')
      }
      
      // 密码修改成功，这里可以添加其他逻辑
      return true
    } catch (error) {
      throw error
    }
  }

  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false
    
    // 超级管理员拥有所有权限
    if (userPermissions.value.some(p => p.code === '*')) return true
    
    // 检查具体权限
    return userPermissions.value.some(p => p.code === permission)
  }

  const hasRole = (roleCode: string): boolean => {
    if (!user.value) return false
    return userRoles.value.some(role => role.code === roleCode)
  }

  const hasAnyRole = (roleCodes: string[]): boolean => {
    if (!user.value) return false
    return roleCodes.some(roleCode => hasRole(roleCode))
  }

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user.value) return false
    return permissions.some(permission => hasPermission(permission))
  }

  const initializeAuth = () => {
    // 从localStorage恢复状态
    const storedUser = localStorage.getItem('user')
    const storedToken = localStorage.getItem('token')
    const storedRefreshToken = localStorage.getItem('refreshToken')
    
    if (storedUser && storedToken) {
      try {
        user.value = JSON.parse(storedUser)
        token.value = storedToken
        refreshToken.value = storedRefreshToken || ''
        isLoggedIn.value = true
      } catch (error) {
        console.error('Failed to parse stored user data:', error)
        // 清除无效数据
        localStorage.removeItem('user')
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
      }
    }
  }

  const getUserInfo = async () => {
    try {
      // 模拟获取用户信息API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (token.value) {
        // 返回当前用户信息
        return user.value
      } else {
        throw new Error('No token found')
      }
    } catch (error) {
      throw error
    }
  }

  // 初始化认证状态
  initializeAuth()

  return {
    // 状态
    user,
    token,
    refreshToken,
    isLoggedIn,
    
    // 计算属性
    userRoles,
    userPermissions,
    isAdmin,
    
    // 方法
    login,
    logout,
    refreshAccessToken,
    updateProfile,
    changePassword,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAnyPermission,
    initializeAuth,
    getUserInfo
  }
})
