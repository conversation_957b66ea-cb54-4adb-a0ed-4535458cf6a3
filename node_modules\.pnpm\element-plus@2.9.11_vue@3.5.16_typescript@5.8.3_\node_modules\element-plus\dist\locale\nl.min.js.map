{"version": 3, "file": "nl.min.js", "sources": ["../../../../packages/locale/lang/nl.ts"], "sourcesContent": ["export default {\n  name: 'nl',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Bevestig',\n      clear: 'Wissen',\n    },\n    datepicker: {\n      now: 'Nu',\n      today: '<PERSON><PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON>',\n      clear: 'Legen',\n      confirm: 'Bevestig',\n      selectDate: 'Selecteer datum',\n      selectTime: 'Selecteer tijd',\n      startDate: 'Startdatum',\n      startTime: 'Starttijd',\n      endDate: 'Einddatum',\n      endTime: 'Eindtijd',\n      prevYear: 'Vorig jaar',\n      nextYear: 'Volgend jaar',\n      prevMonth: 'Vorige maand',\n      nextMonth: 'Volgende maand',\n      year: '',\n      month1: 'januari',\n      month2: 'februari',\n      month3: 'maart',\n      month4: 'april',\n      month5: 'mei',\n      month6: 'juni',\n      month7: 'juli',\n      month8: 'augustus',\n      month9: 'september',\n      month10: 'oktober',\n      month11: 'november',\n      month12: 'december',\n      // week: 'week',\n      weeks: {\n        sun: 'Zo',\n        mon: 'Ma',\n        tue: 'Di',\n        wed: 'Wo',\n        thu: 'Do',\n        fri: 'Vr',\n        sat: 'Za',\n      },\n      months: {\n        jan: 'jan',\n        feb: 'feb',\n        mar: 'maa',\n        apr: 'apr',\n        may: 'mei',\n        jun: 'jun',\n        jul: 'jul',\n        aug: 'aug',\n        sep: 'sep',\n        oct: 'okt',\n        nov: 'nov',\n        dec: 'dec',\n      },\n    },\n    select: {\n      loading: 'Laden',\n      noMatch: 'Geen overeenkomende resultaten',\n      noData: 'Geen data',\n      placeholder: 'Selecteer',\n    },\n    mention: {\n      loading: 'Laden',\n    },\n    cascader: {\n      noMatch: 'Geen overeenkomende resultaten',\n      loading: 'Laden',\n      placeholder: 'Selecteer',\n      noData: 'Geen data',\n    },\n    pagination: {\n      goto: 'Ga naar',\n      pagesize: '/pagina',\n      total: 'Totaal {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Bericht',\n      confirm: 'Bevestig',\n      cancel: 'Annuleren',\n      error: 'Ongeldige invoer',\n    },\n    upload: {\n      deleteTip: 'Kies verwijder om te wissen',\n      delete: 'Verwijder',\n      preview: 'Voorbeeld',\n      continue: 'Doorgaan',\n    },\n    table: {\n      emptyText: 'Geen data',\n      confirmFilter: 'Bevestigen',\n      resetFilter: 'Reset',\n      clearFilter: 'Alles',\n      sumText: 'Som',\n    },\n    tree: {\n      emptyText: 'Geen data',\n    },\n    transfer: {\n      noMatch: 'Geen overeenkomende resultaten',\n      noData: 'Geen data',\n      titles: ['Lijst 1', 'Lijst 2'],\n      filterPlaceholder: 'Geef zoekwoerd',\n      noCheckedFormat: '{total} items',\n      hasCheckedFormat: '{checked}/{total} geselecteerd',\n    },\n    image: {\n      error: 'MISLUKT',\n    },\n    pageHeader: {\n      title: 'Terug',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ja',\n      cancelButtonText: 'Nee',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}