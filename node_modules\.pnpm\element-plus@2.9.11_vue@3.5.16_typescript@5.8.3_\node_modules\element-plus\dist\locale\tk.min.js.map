{"version": 3, "file": "tk.min.js", "sources": ["../../../../packages/locale/lang/tk.ts"], "sourcesContent": ["export default {\n  name: 'tk',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>wagt',\n      today: '<PERSON>ü<PERSON><PERSON><PERSON>',\n      cancel: 'Bes et',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON> sa<PERSON>',\n      selectTime: 'Wagty saýlaň',\n      startDate: '<PERSON><PERSON><PERSON><PERSON><PERSON> güni',\n      startTime: '<PERSON><PERSON><PERSON><PERSON><PERSON> wagty',\n      endDate: 'G<PERSON>r<PERSON><PERSON> güni',\n      endTime: 'G<PERSON>r<PERSON>an wagty',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: '',\n      month1: 'Ýan',\n      month2: 'Few',\n      month3: 'Mar',\n      month4: 'Apr',\n      month5: 'Maý',\n      month6: 'Iýn',\n      month7: 'Iýl',\n      month8: 'Awg',\n      month9: 'Sen',\n      month10: 'Okt',\n      month11: 'Noý',\n      month12: 'Dek',\n      // week: 'week',\n      weeks: {\n        sun: 'Ýek',\n        mon: 'Du<PERSON>',\n        tue: 'Si<PERSON>',\n        wed: 'Çar',\n        thu: 'Pen',\n        fri: 'Ann',\n        sat: 'Şen',\n      },\n      months: {\n        jan: 'Ýan',\n        feb: 'Few',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maý',\n        jun: 'Iýn',\n        jul: 'Iýl',\n        aug: 'Awg',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Noý',\n        dec: 'Dek',\n      },\n    },\n    select: {\n      loading: 'Indirilýär',\n      noMatch: 'Hiçzat tapylmady',\n      noData: 'Hiçzat ýok',\n      placeholder: 'Saýla',\n    },\n    mention: {\n      loading: 'Indirilýär',\n    },\n    cascader: {\n      noMatch: 'Hiçzat tapylmady',\n      loading: 'Indirilýär',\n      placeholder: 'Saýlaň',\n      noData: 'Hiçzat ýok',\n    },\n    pagination: {\n      goto: 'Git',\n      pagesize: '/sahypa',\n      total: 'Umumy {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Hat',\n      confirm: 'OK',\n      cancel: 'Bes et',\n      error: 'Ýalňyş girizme',\n    },\n    upload: {\n      deleteTip: 'Pozmak üçin \"poz\" düwmä basyň',\n      delete: 'Poz',\n      preview: 'Gör',\n      continue: 'Dowam et',\n    },\n    table: {\n      emptyText: 'Maglumat ýok',\n      confirmFilter: 'Tassykla',\n      resetFilter: 'Arassala',\n      clearFilter: 'Hemmesi',\n      sumText: 'Jemi',\n    },\n    tree: {\n      emptyText: 'Maglumat ýok',\n    },\n    transfer: {\n      noMatch: 'Hiçzat tapylmady',\n      noData: 'Hiçzat ýok',\n      titles: ['Sanaw 1', 'Sanaw 2'],\n      filterPlaceholder: 'Gözleg sözlerini giriziň',\n      noCheckedFormat: '{total} sany',\n      hasCheckedFormat: '{checked}/{total} saýlanan',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,0BAA0B,CAAC,SAAS,CAAC,wBAAwB,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC,kBAAkB,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,gDAAgD,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,qCAAqC,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}