{"version": 3, "file": "bg.min.mjs", "sources": ["../../../../packages/locale/lang/bg.ts"], "sourcesContent": ["export default {\n  name: 'bg',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Изчисти',\n    },\n    datepicker: {\n      now: 'Сега',\n      today: 'Днес',\n      cancel: 'Откаж<PERSON>',\n      clear: 'Изчисти',\n      confirm: 'ОК',\n      selectDate: 'Избери дата',\n      selectTime: 'Избери час',\n      startDate: 'Начална дата',\n      startTime: 'Начален час',\n      endDate: 'Крайна дата',\n      endTime: 'Краен час',\n      prevYear: 'Previous Year', // to be translated\n      nextYear: 'Next Year', // to be translated\n      prevMonth: 'Previous Month', // to be translated\n      nextMonth: 'Next Month', // to be translated\n      year: '',\n      month1: 'Януари',\n      month2: 'Февруари',\n      month3: 'Март',\n      month4: 'Април',\n      month5: 'Май',\n      month6: 'Юни',\n      month7: 'Юли',\n      month8: 'Август',\n      month9: 'Септември',\n      month10: 'Октомври',\n      month11: 'Ноември',\n      month12: 'Декември',\n      // week: 'Седмица',\n      weeks: {\n        sun: 'Нед',\n        mon: 'Пон',\n        tue: 'Вто',\n        wed: 'Сря',\n        thu: 'Чет',\n        fri: 'Пет',\n        sat: 'Съб',\n      },\n      months: {\n        jan: 'Яну',\n        feb: 'Фев',\n        mar: 'Мар',\n        apr: 'Апр',\n        may: 'Май',\n        jun: 'Юни',\n        jul: 'Юли',\n        aug: 'Авг',\n        sep: 'Сеп',\n        oct: 'Окт',\n        nov: 'Ное',\n        dec: 'Дек',\n      },\n    },\n    select: {\n      loading: 'Зареждане',\n      noMatch: 'Няма намерени',\n      noData: 'Няма данни',\n      placeholder: 'Избери',\n    },\n    mention: {\n      loading: 'Зареждане',\n    },\n    cascader: {\n      noMatch: 'Няма намерени',\n      loading: 'Зареждане',\n      placeholder: 'Избери',\n      noData: 'Няма данни',\n    },\n    pagination: {\n      goto: 'Иди на',\n      pagesize: '/страница',\n      total: 'Общо {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Съобщение',\n      confirm: 'ОК',\n      cancel: 'Откажи',\n      error: 'Невалидни данни',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'Изтрий',\n      preview: 'Прегледай',\n      continue: 'Продължи',\n    },\n    table: {\n      emptyText: 'Няма данни',\n      confirmFilter: 'Потвърди',\n      resetFilter: 'Изчисти',\n      clearFilter: 'Всички',\n      sumText: 'Sum', // to be translated\n    },\n    tree: {\n      emptyText: 'Няма данни',\n    },\n    transfer: {\n      noMatch: 'Няма намерени',\n      noData: 'Няма данни',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'Enter keyword', // to be translated\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,4CAA4C,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,+DAA+D,CAAC,UAAU,CAAC,yDAAyD,CAAC,SAAS,CAAC,qEAAqE,CAAC,SAAS,CAAC,+DAA+D,CAAC,OAAO,CAAC,+DAA+D,CAAC,OAAO,CAAC,mDAAmD,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,wDAAwD,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,kDAAkD,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,2EAA2E,CAAC,MAAM,CAAC,yDAAyD,CAAC,WAAW,CAAC,sCAAsC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,2EAA2E,CAAC,OAAO,CAAC,wDAAwD,CAAC,WAAW,CAAC,sCAAsC,CAAC,MAAM,CAAC,yDAAyD,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,mDAAmD,CAAC,KAAK,CAAC,kCAAkC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,wDAAwD,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,uFAAuF,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,MAAM,CAAC,sCAAsC,CAAC,OAAO,CAAC,wDAAwD,CAAC,QAAQ,CAAC,kDAAkD,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,aAAa,CAAC,kDAAkD,CAAC,WAAW,CAAC,4CAA4C,CAAC,WAAW,CAAC,sCAAsC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,2EAA2E,CAAC,MAAM,CAAC,yDAAyD,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}