import { createRouter, createWebHistory } from "vue-router";
import HomeView from "../views/HomeView.vue";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "home",
      component: HomeView,
      meta: { title: "仪表板" },
    },
    {
      path: "/vehicle",
      name: "vehicle",
      redirect: "/vehicle/maintenance",
      meta: { title: "车辆管理" },
      children: [
        {
          path: "maintenance",
          name: "vehicle-maintenance",
          component: () => import("../views/AboutView.vue"),
          meta: { title: "维修记录" },
        },
      ],
    },
    {
      path: "/about",
      name: "about",
      component: () => import("../views/AboutView.vue"),
      meta: { title: "关于" },
    },
  ],
});

export default router;
