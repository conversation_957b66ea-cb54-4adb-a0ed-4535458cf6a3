@import "tailwindcss";

/* 安全系统设计系统 - 基于Tailwind CSS 4.0 */

/* 自定义CSS变量 */
@layer base {
  :root {
    /* Primary 主色 - 渐变蓝紫色系 */
    --color-primary-50: #f0f4ff;
    --color-primary-100: #e0e9ff;
    --color-primary-200: #c7d6fe;
    --color-primary-300: #a5b8fc;
    --color-primary-400: #8592f8;
    --color-primary-500: #667eea;  /* 主色 */
    --color-primary-600: #5a67d8;
    --color-primary-700: #4c51bf;
    --color-primary-800: #434190;
    --color-primary-900: #3c366b;

    /* Secondary 次色 */
    --color-secondary-50: #faf5ff;
    --color-secondary-100: #f3e8ff;
    --color-secondary-200: #e9d5ff;
    --color-secondary-300: #d8b4fe;
    --color-secondary-400: #c084fc;
    --color-secondary-500: #a855f7;
    --color-secondary-600: #9333ea;
    --color-secondary-700: #764ba2;  /* 渐变终点色 */
    --color-secondary-800: #6b21a8;
    --color-secondary-900: #581c87;

    /* Success 成功色 - 绿色系 */
    --color-success-50: #f0fff4;
    --color-success-100: #c6f6d5;
    --color-success-500: #38a169;
    --color-success-600: #2f855a;
    --color-success-700: #276749;

    /* Warning 警告色 - 橙色系 */
    --color-warning-50: #fffbeb;
    --color-warning-100: #feebc8;
    --color-warning-500: #dd6b20;
    --color-warning-600: #c05621;

    /* Danger 危险色 - 红色系 */
    --color-danger-50: #fff5f5;
    --color-danger-100: #fed7d7;
    --color-danger-500: #e53e3e;
    --color-danger-600: #c53030;

    /* Info 信息色 - 蓝色系 */
    --color-info-50: #ebf8ff;
    --color-info-100: #bee3f8;
    --color-info-500: #3182ce;
    --color-info-600: #2b77cb;

    /* 语义化颜色 */
    --color-bg-primary: #ffffff;
    --color-bg-secondary: #f7fafc;
    --color-bg-tertiary: #edf2f7;
    --color-text-primary: #1a202c;
    --color-text-secondary: #4a5568;
    --color-text-tertiary: #718096;
    --color-border-primary: #e2e8f0;
    --color-border-secondary: #cbd5e0;

    /* 字体系统 */
    --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                        'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 
                        sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', 
                        Consolas, 'Courier New', monospace;

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* 彩色阴影 */
    --shadow-primary: 0 4px 12px rgba(102, 126, 234, 0.15);
    --shadow-success: 0 4px 12px rgba(56, 161, 105, 0.15);
    --shadow-warning: 0 4px 12px rgba(221, 107, 32, 0.15);
    --shadow-danger: 0 4px 12px rgba(229, 62, 62, 0.15);
  }

  /* 基础样式重置 */
  * {
    box-sizing: border-box;
  }

  html {
    font-family: var(--font-family-sans);
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
  }

  body {
    margin: 0;
    font-family: inherit;
    line-height: inherit;
    color: var(--color-text-primary);
    background-color: var(--color-bg-secondary);
  }
}

/* 组件样式 */
@layer components {
  /* 按钮基础样式 */
  .btn-base {
    @apply inline-flex items-center justify-center font-medium 
           transition-all duration-200 ease-in-out
           focus:outline-none focus:ring-2 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* 按钮变体 */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 
           text-white hover:shadow-lg
           focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-700 hover:bg-gray-200
           focus:ring-gray-500;
  }

  .btn-outline {
    @apply border border-gray-300 text-gray-700 bg-transparent
           hover:bg-gray-50 focus:ring-gray-500;
  }

  /* 按钮尺寸 */
  .btn-xs { @apply px-2 py-1 text-xs rounded-md; }
  .btn-sm { @apply px-3 py-1.5 text-sm rounded-md; }
  .btn-md { @apply px-4 py-2 text-sm rounded-lg; }
  .btn-lg { @apply px-6 py-3 text-base rounded-lg; }
  .btn-xl { @apply px-8 py-4 text-lg rounded-xl; }

  /* 卡片组件 */
  .card-base {
    @apply bg-white rounded-xl overflow-hidden
           transition-all duration-300 ease-in-out;
  }

  .card-shadow-sm { @apply shadow-sm; }
  .card-shadow-md { @apply shadow-md; }
  .card-shadow-lg { @apply shadow-lg; }

  .card-bordered { @apply border border-gray-200; }

  .card-hoverable {
    @apply hover:shadow-lg hover:-translate-y-1;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200
           bg-gray-50 flex items-center justify-between;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200
           bg-gray-50 flex items-center justify-end gap-3;
  }

  /* 输入框样式 */
  .input-base {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg
           text-sm placeholder-gray-400
           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
           disabled:bg-gray-100 disabled:cursor-not-allowed
           transition-all duration-200;
  }

  .input-error {
    @apply border-red-500 focus:ring-red-500;
  }

  .input-success {
    @apply border-green-500 focus:ring-green-500;
  }

  /* 表格样式 */
  .data-table {
    @apply w-full border-collapse;
  }

  .data-table th {
    @apply px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .data-table td {
    @apply px-4 py-4 whitespace-nowrap text-sm text-gray-900;
  }
}

/* 工具类 */
@layer utilities {
  /* 渐变背景 */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  }

  .bg-gradient-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  }

  .bg-gradient-danger {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  }

  /* 文本渐变 */
  .text-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 动画效果 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .slide-enter-active,
  .slide-leave-active {
    transition: transform 0.3s ease;
  }

  .slide-enter-from {
    transform: translateX(-100%);
  }

  .slide-leave-to {
    transform: translateX(100%);
  }
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  @apply bg-gradient-primary border-0;
}

.el-button--primary:hover {
  @apply shadow-lg;
}

/* 响应式工具类 */
@media (max-width: 640px) {
  .mobile-hidden {
    @apply hidden;
  }
}

@media (min-width: 768px) {
  .desktop-only {
    @apply block;
  }
}
