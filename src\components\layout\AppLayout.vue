<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <el-header class="app-header">
      <div class="header-left">
        <div class="logo">
          <img src="/favicon.ico" alt="Logo" class="logo-img" />
          <span class="logo-text">安全管理系统</span>
        </div>
      </div>

      <div class="header-right">
        <!-- 系统信息 -->
        <div class="system-info">
          <span class="current-time">{{ currentTime }}</span>
        </div>
      </div>
    </el-header>

    <el-container class="app-container">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="app-sidebar">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="item in menuItems" :key="item.path">
            <el-sub-menu
              v-if="item.children && item.children.length > 0"
              :index="item.path"
            >
              <template #title>
                <el-icon><component :is="item.icon" /></el-icon>
                <span>{{ item.meta?.title }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon><component :is="child.icon" /></el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item v-else :index="item.path">
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.meta?.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>

        <!-- 折叠按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon><Fold v-if="!isCollapsed" /><Expand v-else /></el-icon>
        </div>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="app-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 页面内容 -->
        <div class="page-content">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import {
  Fold,
  Expand,
  House,
  Car,
  User,
  Shield,
  Document,
  DataAnalysis,
} from "@element-plus/icons-vue";
import type { MenuItem } from "@/types";
import { formatDateTime } from "@/utils";

// 状态管理
const route = useRoute();

// 响应式数据
const isCollapsed = ref(false);
const currentTime = ref("");
let timeInterval: NodeJS.Timeout;

// 计算属性
const sidebarWidth = computed(() => (isCollapsed.value ? "64px" : "200px"));
const activeMenu = computed(() => route.path);

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter((item) => item.meta?.title);
  return matched.map((item) => ({
    title: item.meta?.title as string,
    path: item.path,
  }));
});

// 菜单配置
const menuItems: MenuItem[] = [
  {
    id: "dashboard",
    name: "Dashboard",
    path: "/",
    icon: "House",
    meta: { title: "仪表板" },
  },
  {
    id: "vehicle",
    name: "Vehicle",
    path: "/vehicle",
    icon: "Car",
    meta: { title: "车辆管理" },
    children: [
      {
        id: "vehicle-list",
        name: "VehicleList",
        path: "/vehicle/list",
        icon: "Car",
        meta: { title: "车辆列表" },
      },
      {
        id: "vehicle-maintenance",
        name: "VehicleMaintenance",
        path: "/vehicle/maintenance",
        icon: "Car",
        meta: { title: "维修记录" },
      },
    ],
  },
  {
    id: "personnel",
    name: "Personnel",
    path: "/personnel",
    icon: "User",
    meta: { title: "人员管理" },
    children: [
      {
        id: "personnel-list",
        name: "PersonnelList",
        path: "/personnel/list",
        icon: "User",
        meta: { title: "人员列表" },
      },
      {
        id: "personnel-training",
        name: "PersonnelTraining",
        path: "/personnel/training",
        icon: "User",
        meta: { title: "培训记录" },
      },
    ],
  },
  {
    id: "safety",
    name: "Safety",
    path: "/safety",
    icon: "Shield",
    meta: { title: "安全检查" },
    children: [
      {
        id: "safety-inspection",
        name: "SafetyInspection",
        path: "/safety/inspection",
        icon: "Shield",
        meta: { title: "安全检查" },
      },
      {
        id: "safety-hazard",
        name: "SafetyHazard",
        path: "/safety/hazard",
        icon: "Shield",
        meta: { title: "隐患管理" },
      },
    ],
  },
  {
    id: "training",
    name: "Training",
    path: "/training",
    icon: "Document",
    meta: { title: "培训考试" },
  },
  {
    id: "reports",
    name: "Reports",
    path: "/reports",
    icon: "DataAnalysis",
    meta: { title: "统计报表" },
  },
];

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};

const updateTime = () => {
  currentTime.value = formatDateTime(new Date());
};

// 生命周期
onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});
</script>

<style scoped>
.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  @apply bg-white border-b border-gray-200 px-6 flex items-center justify-between;
  height: 60px !important;
}

.header-left {
  @apply flex items-center;
}

.logo {
  @apply flex items-center gap-3;
}

.logo-img {
  @apply w-8 h-8;
}

.logo-text {
  @apply text-xl font-bold text-gradient-primary;
}

.header-right {
  @apply flex items-center gap-4;
}

.system-info {
  @apply flex items-center gap-2;
}

.current-time {
  @apply text-sm font-medium text-gray-600 bg-gray-100 px-3 py-1 rounded-lg;
}

.app-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.app-sidebar {
  @apply bg-white border-r border-gray-200 relative;
  transition: width 0.3s ease;
}

.sidebar-menu {
  @apply border-none h-full;
}

.sidebar-toggle {
  @apply absolute bottom-4 left-1/2 transform -translate-x-1/2 
         w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center 
         cursor-pointer hover:bg-gray-200 transition-colors;
}

.app-main {
  @apply bg-gray-50 p-0;
}

.breadcrumb-container {
  @apply bg-white px-6 py-3 border-b border-gray-200;
}

.page-content {
  @apply p-6;
}

/* Element Plus 样式覆盖 */
:deep(.el-menu-item.is-active) {
  @apply bg-gradient-primary text-white;
}

:deep(.el-menu-item:hover) {
  @apply bg-blue-50;
}

:deep(.el-sub-menu__title:hover) {
  @apply bg-blue-50;
}

:deep(.el-menu--collapse .el-menu-item) {
  @apply text-center;
}
</style>
