<template>
  <div class="app-layout">
    <!-- 顶部导航栏 -->
    <el-header class="app-header">
      <div class="header-left">
        <div class="logo">
          <img src="/favicon.ico" alt="Logo" class="logo-img">
          <span class="logo-text">安全管理系统</span>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 通知 -->
        <el-badge :value="unreadCount" class="notification-badge">
          <el-button :icon="Bell" circle />
        </el-badge>
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand">
          <div class="user-info">
            <el-avatar :src="userStore.user?.avatar" :size="32">
              {{ userStore.user?.name?.charAt(0) }}
            </el-avatar>
            <span class="username">{{ userStore.user?.name }}</span>
            <el-icon><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="settings">系统设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="app-container">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="app-sidebar">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="item in menuItems" :key="item.path">
            <el-sub-menu v-if="item.children && item.children.length > 0" :index="item.path">
              <template #title>
                <el-icon><component :is="item.icon" /></el-icon>
                <span>{{ item.meta?.title }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children"
                :key="child.path"
                :index="child.path"
              >
                <el-icon><component :is="child.icon" /></el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            
            <el-menu-item v-else :index="item.path">
              <el-icon><component :is="item.icon" /></el-icon>
              <span>{{ item.meta?.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
        
        <!-- 折叠按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon><Fold v-if="!isCollapsed" /><Expand v-else /></el-icon>
        </div>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="app-main">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Bell,
  ArrowDown,
  Fold,
  Expand,
  House,
  Car,
  User,
  Shield,
  Document,
  DataAnalysis
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { MenuItem } from '@/types'

// 状态管理
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()

// 响应式数据
const isCollapsed = ref(false)
const unreadCount = ref(5)

// 计算属性
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '200px')
const activeMenu = computed(() => route.path)

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    title: item.meta?.title as string,
    path: item.path
  }))
})

// 菜单配置
const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    path: '/',
    icon: 'House',
    meta: { title: '仪表板', requiresAuth: true }
  },
  {
    id: 'vehicle',
    name: 'Vehicle',
    path: '/vehicle',
    icon: 'Car',
    meta: { title: '车辆管理', requiresAuth: true },
    children: [
      {
        id: 'vehicle-list',
        name: 'VehicleList',
        path: '/vehicle/list',
        icon: 'Car',
        meta: { title: '车辆列表', requiresAuth: true }
      },
      {
        id: 'vehicle-maintenance',
        name: 'VehicleMaintenance',
        path: '/vehicle/maintenance',
        icon: 'Car',
        meta: { title: '维修记录', requiresAuth: true }
      }
    ]
  },
  {
    id: 'personnel',
    name: 'Personnel',
    path: '/personnel',
    icon: 'User',
    meta: { title: '人员管理', requiresAuth: true },
    children: [
      {
        id: 'personnel-list',
        name: 'PersonnelList',
        path: '/personnel/list',
        icon: 'User',
        meta: { title: '人员列表', requiresAuth: true }
      },
      {
        id: 'personnel-training',
        name: 'PersonnelTraining',
        path: '/personnel/training',
        icon: 'User',
        meta: { title: '培训记录', requiresAuth: true }
      }
    ]
  },
  {
    id: 'safety',
    name: 'Safety',
    path: '/safety',
    icon: 'Shield',
    meta: { title: '安全检查', requiresAuth: true },
    children: [
      {
        id: 'safety-inspection',
        name: 'SafetyInspection',
        path: '/safety/inspection',
        icon: 'Shield',
        meta: { title: '安全检查', requiresAuth: true }
      },
      {
        id: 'safety-hazard',
        name: 'SafetyHazard',
        path: '/safety/hazard',
        icon: 'Shield',
        meta: { title: '隐患管理', requiresAuth: true }
      }
    ]
  },
  {
    id: 'training',
    name: 'Training',
    path: '/training',
    icon: 'Document',
    meta: { title: '培训考试', requiresAuth: true }
  },
  {
    id: 'reports',
    name: 'Reports',
    path: '/reports',
    icon: 'DataAnalysis',
    meta: { title: '统计报表', requiresAuth: true }
  }
]

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  } catch (error) {
    ElMessage.error('退出登录失败')
  }
}

// 监听路由变化
watch(
  () => route.path,
  () => {
    // 可以在这里处理路由变化的逻辑
  }
)
</script>

<style scoped>
.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  @apply bg-white border-b border-gray-200 px-6 flex items-center justify-between;
  height: 60px !important;
}

.header-left {
  @apply flex items-center;
}

.logo {
  @apply flex items-center gap-3;
}

.logo-img {
  @apply w-8 h-8;
}

.logo-text {
  @apply text-xl font-bold text-gradient-primary;
}

.header-right {
  @apply flex items-center gap-4;
}

.notification-badge {
  @apply cursor-pointer;
}

.user-info {
  @apply flex items-center gap-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors;
}

.username {
  @apply text-sm font-medium text-gray-700;
}

.app-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.app-sidebar {
  @apply bg-white border-r border-gray-200 relative;
  transition: width 0.3s ease;
}

.sidebar-menu {
  @apply border-none h-full;
}

.sidebar-toggle {
  @apply absolute bottom-4 left-1/2 transform -translate-x-1/2 
         w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center 
         cursor-pointer hover:bg-gray-200 transition-colors;
}

.app-main {
  @apply bg-gray-50 p-0;
}

.breadcrumb-container {
  @apply bg-white px-6 py-3 border-b border-gray-200;
}

.page-content {
  @apply p-6;
}

/* Element Plus 样式覆盖 */
:deep(.el-menu-item.is-active) {
  @apply bg-gradient-primary text-white;
}

:deep(.el-menu-item:hover) {
  @apply bg-blue-50;
}

:deep(.el-sub-menu__title:hover) {
  @apply bg-blue-50;
}

:deep(.el-menu--collapse .el-menu-item) {
  @apply text-center;
}
</style>
