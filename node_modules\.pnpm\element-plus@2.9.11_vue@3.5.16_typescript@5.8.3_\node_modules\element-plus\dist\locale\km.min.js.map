{"version": 3, "file": "km.min.js", "sources": ["../../../../packages/locale/lang/km.ts"], "sourcesContent": ["export default {\n  name: 'km',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'យល់ព្រម',\n      clear: 'លុប',\n    },\n    datepicker: {\n      now: 'ឥឡូវ​នេះ',\n      today: 'ថ្ងៃនេះ',\n      cancel: 'បោះបង់',\n      clear: 'លុប',\n      confirm: 'យល់ព្រម',\n      selectDate: 'ជ្រើសរើសថ្ងៃ',\n      selectTime: 'ជ្រើសរើសម៉ោង',\n      startDate: 'ថ្ងៃចាប់ផ្តើម',\n      startTime: 'ម៉ោងចាប់ផ្តើម',\n      endDate: 'ថ្ងៃបញ្ចប់',\n      endTime: 'ម៉ោងបញ្ចប់',\n      prevYear: 'ឆ្នាំមុន',\n      nextYear: 'ឆ្នាំក្រោយ',\n      prevMonth: 'ខែមុន',\n      nextMonth: 'ខែក្រោយ',\n      year: 'ឆ្នាំ',\n      month1: 'មករា',\n      month2: 'កុម្ភៈ',\n      month3: 'មីនា',\n      month4: 'មេសា',\n      month5: 'ឧសភា',\n      month6: 'មិថុនា',\n      month7: 'កក្កដា',\n      month8: 'សីហា',\n      month9: 'កញ្ញា',\n      month10: 'តុលា',\n      month11: 'វិច្ឆិកា',\n      month12: 'ធ្នូ',\n      // week: 'សប្តាហ៍',\n      weeks: {\n        sun: 'អាទិត្យ',\n        mon: 'ចន្ទ',\n        tue: 'អង្គារ',\n        wed: 'ពុធ',\n        thu: 'ព្រហ',\n        fri: 'សុក្រ',\n        sat: 'សៅរ៍',\n      },\n      months: {\n        jan: 'មករា',\n        feb: 'កុម្ភៈ',\n        mar: 'មីនា',\n        apr: 'មេសា',\n        may: 'ឧសភា',\n        jun: 'មិថុនា',\n        jul: 'កក្កដា',\n        aug: 'សីហា',\n        sep: 'កញ្ញា',\n        oct: 'តុលា',\n        nov: 'វិច្ឆិកា',\n        dec: 'ធ្នូ',\n      },\n    },\n    select: {\n      loading: 'កំពុងផ្ទុក',\n      noMatch: 'គ្មានទិន្នន័យដូច',\n      noData: 'គ្មានទិន្នន័យ',\n      placeholder: 'ជ្រើសរើស',\n    },\n    mention: {\n      loading: 'កំពុងផ្ទុក',\n    },\n    cascader: {\n      noMatch: 'គ្មានទិន្នន័យដូច',\n      loading: 'កំពុងផ្ទុក',\n      placeholder: 'ជ្រើសរើស',\n      noData: 'គ្មានទិន្នន័យ',\n    },\n    pagination: {\n      goto: 'ទៅកាន់',\n      pagesize: '/ទំព័រ',\n      total: 'សរុប {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'សារ',\n      confirm: 'យល់ព្រម',\n      cancel: 'បោះបង់',\n      error: 'ការបញ្ចូលមិនត្រូវបានអនុញ្ញាត',\n    },\n    upload: {\n      deleteTip: 'ចុចលុបដើម្បីដកចេញ',\n      delete: 'លុប',\n      preview: 'មើល',\n      continue: 'បន្ត',\n    },\n    table: {\n      emptyText: 'គ្មានទិន្នន័យ',\n      confirmFilter: 'យល់ព្រម',\n      resetFilter: 'កំណត់ឡើងវិញ',\n      clearFilter: 'ទាំងអស់',\n      sumText: 'បូក',\n    },\n    tree: {\n      emptyText: 'គ្មានទិន្នន័យ',\n    },\n    transfer: {\n      noMatch: 'គ្មានទិន្នន័យដូច',\n      noData: 'គ្មានទិន្នន័យ',\n      titles: ['បញ្ជី ១', 'បញ្ជី ២'],\n      filterPlaceholder: 'បញ្ចូលពាក្យ',\n      noCheckedFormat: '{total} ធាតុ',\n      hasCheckedFormat: '{checked}/{total} បានជ្រើសយក',\n    },\n    image: {\n      error: 'មិនបានជោគជ័យ',\n    },\n    pageHeader: {\n      title: 'ត្រលប់ក្រោយ',\n    },\n    popconfirm: {\n      confirmButtonText: 'យល់ព្រម',\n      cancelButtonText: 'មិនព្រម',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,4CAA4C,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,kDAAkD,CAAC,KAAK,CAAC,4CAA4C,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,4CAA4C,CAAC,UAAU,CAAC,0EAA0E,CAAC,UAAU,CAAC,0EAA0E,CAAC,SAAS,CAAC,gFAAgF,CAAC,SAAS,CAAC,gFAAgF,CAAC,OAAO,CAAC,8DAA8D,CAAC,OAAO,CAAC,8DAA8D,CAAC,QAAQ,CAAC,kDAAkD,CAAC,QAAQ,CAAC,8DAA8D,CAAC,SAAS,CAAC,gCAAgC,CAAC,SAAS,CAAC,4CAA4C,CAAC,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,0BAA0B,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,8DAA8D,CAAC,OAAO,CAAC,kGAAkG,CAAC,MAAM,CAAC,gFAAgF,CAAC,WAAW,CAAC,kDAAkD,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,8DAA8D,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kGAAkG,CAAC,OAAO,CAAC,8DAA8D,CAAC,WAAW,CAAC,kDAAkD,CAAC,MAAM,CAAC,gFAAgF,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,QAAQ,CAAC,iCAAiC,CAAC,KAAK,CAAC,kCAAkC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,4CAA4C,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,0KAA0K,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wGAAwG,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,gFAAgF,CAAC,aAAa,CAAC,4CAA4C,CAAC,WAAW,CAAC,oEAAoE,CAAC,WAAW,CAAC,4CAA4C,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gFAAgF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kGAAkG,CAAC,MAAM,CAAC,gFAAgF,CAAC,MAAM,CAAC,CAAC,uCAAuC,CAAC,uCAAuC,CAAC,CAAC,iBAAiB,CAAC,oEAAoE,CAAC,eAAe,CAAC,kCAAkC,CAAC,gBAAgB,CAAC,gFAAgF,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,4CAA4C,CAAC,gBAAgB,CAAC,4CAA4C,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}