<script setup lang="ts">
import { RouterView } from "vue-router";
import AppLayout from "@/components/layout/AppLayout.vue";
</script>

<template>
  <div id="app">
    <AppLayout>
      <RouterView />
    </AppLayout>
  </div>
</template>

<style>
#app {
  height: 100vh;
  font-family: var(--font-family-sans);
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 全局样式覆盖 */
.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b21a8 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.el-menu-item.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

.el-menu-item:hover {
  background-color: #f0f4ff !important;
  color: #667eea !important;
}

.el-sub-menu__title:hover {
  background-color: #f0f4ff !important;
  color: #667eea !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .desktop-only {
    display: block !important;
  }
}
</style>
