<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Car, User, Shield, Warning } from "@element-plus/icons-vue";
import type { DashboardStats } from "@/types";

// 响应式数据
const loading = ref(true);
const stats = ref<DashboardStats>({
  vehicleStats: {
    total: 0,
    active: 0,
    maintenance: 0,
    retired: 0,
  },
  personnelStats: {
    total: 0,
    drivers: 0,
    escorts: 0,
    managers: 0,
  },
  safetyStats: {
    inspections: 0,
    hazards: 0,
    violations: 0,
    accidents: 0,
  },
  trends: {
    inspectionTrend: [],
    hazardTrend: [],
    violationTrend: [],
  },
});

// 模拟数据
const mockStats: DashboardStats = {
  vehicleStats: {
    total: 156,
    active: 142,
    maintenance: 12,
    retired: 2,
  },
  personnelStats: {
    total: 89,
    drivers: 45,
    escorts: 28,
    managers: 16,
  },
  safetyStats: {
    inspections: 23,
    hazards: 5,
    violations: 2,
    accidents: 0,
  },
  trends: {
    inspectionTrend: [
      { date: "2024-01", value: 18 },
      { date: "2024-02", value: 22 },
      { date: "2024-03", value: 25 },
      { date: "2024-04", value: 23 },
      { date: "2024-05", value: 27 },
      { date: "2024-06", value: 23 },
    ],
    hazardTrend: [
      { date: "2024-01", value: 8 },
      { date: "2024-02", value: 6 },
      { date: "2024-03", value: 4 },
      { date: "2024-04", value: 7 },
      { date: "2024-05", value: 3 },
      { date: "2024-06", value: 5 },
    ],
    violationTrend: [
      { date: "2024-01", value: 3 },
      { date: "2024-02", value: 1 },
      { date: "2024-03", value: 2 },
      { date: "2024-04", value: 4 },
      { date: "2024-05", value: 1 },
      { date: "2024-06", value: 2 },
    ],
  },
};

// 方法
const fetchDashboardData = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    stats.value = mockStats;
  } catch (error) {
    console.error("Failed to fetch dashboard data:", error);
  } finally {
    loading.value = false;
  }
};

// 生命周期
onMounted(() => {
  fetchDashboardData();
});
</script>

<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1 class="page-title">仪表板</h1>
      <p class="page-subtitle">安全管理系统概览</p>
    </div>

    <div v-loading="loading" class="dashboard-content">
      <!-- 统计卡片 -->
      <div class="stats-grid">
        <!-- 车辆统计 -->
        <el-card class="stat-card vehicle-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><Car /></el-icon>
            </div>
            <div class="stat-info">
              <h3 class="stat-title">车辆总数</h3>
              <p class="stat-number">{{ stats.vehicleStats.total }}</p>
              <div class="stat-details">
                <span class="detail-item">
                  在用: {{ stats.vehicleStats.active }}
                </span>
                <span class="detail-item">
                  维修: {{ stats.vehicleStats.maintenance }}
                </span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 人员统计 -->
        <el-card class="stat-card personnel-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><User /></el-icon>
            </div>
            <div class="stat-info">
              <h3 class="stat-title">人员总数</h3>
              <p class="stat-number">{{ stats.personnelStats.total }}</p>
              <div class="stat-details">
                <span class="detail-item">
                  驾驶员: {{ stats.personnelStats.drivers }}
                </span>
                <span class="detail-item">
                  押运员: {{ stats.personnelStats.escorts }}
                </span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 安全检查 -->
        <el-card class="stat-card safety-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><Shield /></el-icon>
            </div>
            <div class="stat-info">
              <h3 class="stat-title">本月检查</h3>
              <p class="stat-number">{{ stats.safetyStats.inspections }}</p>
              <div class="stat-details">
                <span class="detail-item">
                  隐患: {{ stats.safetyStats.hazards }}
                </span>
                <span class="detail-item">
                  违章: {{ stats.safetyStats.violations }}
                </span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 安全状态 -->
        <el-card class="stat-card warning-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon size="32"><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <h3 class="stat-title">安全事故</h3>
              <p class="stat-number">{{ stats.safetyStats.accidents }}</p>
              <div class="stat-details">
                <span class="detail-item success"> 安全运行 </span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard {
  @apply p-0;
}

.dashboard-header {
  @apply mb-6;
}

.page-title {
  @apply text-3xl font-bold text-gray-800 mb-2;
}

.page-subtitle {
  @apply text-gray-600;
}

.dashboard-content {
  @apply space-y-6;
}

.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.stat-card {
  @apply card-base card-hoverable;
  transition: all 0.3s ease;
}

.stat-card:hover {
  @apply shadow-lg -translate-y-1;
}

.stat-content {
  @apply flex items-center gap-4;
}

.stat-icon {
  @apply w-16 h-16 rounded-full flex items-center justify-center text-white;
}

.vehicle-card .stat-icon {
  @apply bg-gradient-to-r from-blue-500 to-blue-600;
}

.personnel-card .stat-icon {
  @apply bg-gradient-to-r from-green-500 to-green-600;
}

.safety-card .stat-icon {
  @apply bg-gradient-to-r from-purple-500 to-purple-600;
}

.warning-card .stat-icon {
  @apply bg-gradient-to-r from-orange-500 to-orange-600;
}

.stat-info {
  @apply flex-1;
}

.stat-title {
  @apply text-sm font-medium text-gray-600 mb-1;
}

.stat-number {
  @apply text-2xl font-bold text-gray-800 mb-2;
}

.stat-details {
  @apply flex gap-4 text-xs text-gray-500;
}

.detail-item {
  @apply px-2 py-1 bg-gray-100 rounded;
}

.detail-item.success {
  @apply bg-green-100 text-green-700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    @apply grid-cols-1 gap-4;
  }

  .stat-content {
    @apply flex-col text-center gap-2;
  }

  .stat-icon {
    @apply w-12 h-12;
  }
}
</style>
