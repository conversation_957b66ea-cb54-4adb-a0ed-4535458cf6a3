/*! Element Plus v2.9.11 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleBg = factory());
})(this, (function () { 'use strict';

  var bg = {
    name: "bg",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "OK",
        clear: "\u0418\u0437\u0447\u0438\u0441\u0442\u0438"
      },
      datepicker: {
        now: "\u0421\u0435\u0433\u0430",
        today: "\u0414\u043D\u0435\u0441",
        cancel: "\u041E\u0442\u043A\u0430\u0436\u0438",
        clear: "\u0418\u0437\u0447\u0438\u0441\u0442\u0438",
        confirm: "\u041E\u041A",
        selectDate: "\u0418\u0437\u0431\u0435\u0440\u0438 \u0434\u0430\u0442\u0430",
        selectTime: "\u0418\u0437\u0431\u0435\u0440\u0438 \u0447\u0430\u0441",
        startDate: "\u041D\u0430\u0447\u0430\u043B\u043D\u0430 \u0434\u0430\u0442\u0430",
        startTime: "\u041D\u0430\u0447\u0430\u043B\u0435\u043D \u0447\u0430\u0441",
        endDate: "\u041A\u0440\u0430\u0439\u043D\u0430 \u0434\u0430\u0442\u0430",
        endTime: "\u041A\u0440\u0430\u0435\u043D \u0447\u0430\u0441",
        prevYear: "Previous Year",
        nextYear: "Next Year",
        prevMonth: "Previous Month",
        nextMonth: "Next Month",
        year: "",
        month1: "\u042F\u043D\u0443\u0430\u0440\u0438",
        month2: "\u0424\u0435\u0432\u0440\u0443\u0430\u0440\u0438",
        month3: "\u041C\u0430\u0440\u0442",
        month4: "\u0410\u043F\u0440\u0438\u043B",
        month5: "\u041C\u0430\u0439",
        month6: "\u042E\u043D\u0438",
        month7: "\u042E\u043B\u0438",
        month8: "\u0410\u0432\u0433\u0443\u0441\u0442",
        month9: "\u0421\u0435\u043F\u0442\u0435\u043C\u0432\u0440\u0438",
        month10: "\u041E\u043A\u0442\u043E\u043C\u0432\u0440\u0438",
        month11: "\u041D\u043E\u0435\u043C\u0432\u0440\u0438",
        month12: "\u0414\u0435\u043A\u0435\u043C\u0432\u0440\u0438",
        weeks: {
          sun: "\u041D\u0435\u0434",
          mon: "\u041F\u043E\u043D",
          tue: "\u0412\u0442\u043E",
          wed: "\u0421\u0440\u044F",
          thu: "\u0427\u0435\u0442",
          fri: "\u041F\u0435\u0442",
          sat: "\u0421\u044A\u0431"
        },
        months: {
          jan: "\u042F\u043D\u0443",
          feb: "\u0424\u0435\u0432",
          mar: "\u041C\u0430\u0440",
          apr: "\u0410\u043F\u0440",
          may: "\u041C\u0430\u0439",
          jun: "\u042E\u043D\u0438",
          jul: "\u042E\u043B\u0438",
          aug: "\u0410\u0432\u0433",
          sep: "\u0421\u0435\u043F",
          oct: "\u041E\u043A\u0442",
          nov: "\u041D\u043E\u0435",
          dec: "\u0414\u0435\u043A"
        }
      },
      select: {
        loading: "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435",
        noMatch: "\u041D\u044F\u043C\u0430 \u043D\u0430\u043C\u0435\u0440\u0435\u043D\u0438",
        noData: "\u041D\u044F\u043C\u0430 \u0434\u0430\u043D\u043D\u0438",
        placeholder: "\u0418\u0437\u0431\u0435\u0440\u0438"
      },
      mention: {
        loading: "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435"
      },
      cascader: {
        noMatch: "\u041D\u044F\u043C\u0430 \u043D\u0430\u043C\u0435\u0440\u0435\u043D\u0438",
        loading: "\u0417\u0430\u0440\u0435\u0436\u0434\u0430\u043D\u0435",
        placeholder: "\u0418\u0437\u0431\u0435\u0440\u0438",
        noData: "\u041D\u044F\u043C\u0430 \u0434\u0430\u043D\u043D\u0438"
      },
      pagination: {
        goto: "\u0418\u0434\u0438 \u043D\u0430",
        pagesize: "/\u0441\u0442\u0440\u0430\u043D\u0438\u0446\u0430",
        total: "\u041E\u0431\u0449\u043E {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "\u0421\u044A\u043E\u0431\u0449\u0435\u043D\u0438\u0435",
        confirm: "\u041E\u041A",
        cancel: "\u041E\u0442\u043A\u0430\u0436\u0438",
        error: "\u041D\u0435\u0432\u0430\u043B\u0438\u0434\u043D\u0438 \u0434\u0430\u043D\u043D\u0438"
      },
      upload: {
        deleteTip: "press delete to remove",
        delete: "\u0418\u0437\u0442\u0440\u0438\u0439",
        preview: "\u041F\u0440\u0435\u0433\u043B\u0435\u0434\u0430\u0439",
        continue: "\u041F\u0440\u043E\u0434\u044A\u043B\u0436\u0438"
      },
      table: {
        emptyText: "\u041D\u044F\u043C\u0430 \u0434\u0430\u043D\u043D\u0438",
        confirmFilter: "\u041F\u043E\u0442\u0432\u044A\u0440\u0434\u0438",
        resetFilter: "\u0418\u0437\u0447\u0438\u0441\u0442\u0438",
        clearFilter: "\u0412\u0441\u0438\u0447\u043A\u0438",
        sumText: "Sum"
      },
      tree: {
        emptyText: "\u041D\u044F\u043C\u0430 \u0434\u0430\u043D\u043D\u0438"
      },
      transfer: {
        noMatch: "\u041D\u044F\u043C\u0430 \u043D\u0430\u043C\u0435\u0440\u0435\u043D\u0438",
        noData: "\u041D\u044F\u043C\u0430 \u0434\u0430\u043D\u043D\u0438",
        titles: ["List 1", "List 2"],
        filterPlaceholder: "Enter keyword",
        noCheckedFormat: "{total} items",
        hasCheckedFormat: "{checked}/{total} checked"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return bg;

}));
