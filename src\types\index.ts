// 安全系统前端类型定义

// 通用API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
  requestId?: string;
}

// 分页数据类型
export interface PaginatedData<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 分页查询参数
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// 查询参数基类
export interface QueryParams extends PaginationParams {
  keyword?: string;
  filters?: Record<string, any>;
  fields?: string[];
}

// 基础用户信息类型（仅用于显示）
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar: string;
  department: string;
  position: string;
}

// 车辆相关类型
export interface Vehicle {
  id: string;
  plateNumber: string; // 车牌号
  vehicleType: VehicleType; // 车型
  brand: string; // 品牌
  model: string; // 型号
  year: number; // 年份
  vin: string; // VIN码
  engineNumber: string; // 发动机号
  color: string; // 颜色
  loadCapacity: number; // 载重量(吨)
  seatCount: number; // 座位数
  fuelType: FuelType; // 燃料类型
  status: VehicleStatus; // 状态
  driverId?: string; // 当前驾驶员ID
  certificates: Certificate[]; // 证件信息
  createdAt: string;
  updatedAt: string;
}

export type VehicleType = "truck" | "bus" | "van" | "motorcycle" | "car";
export type FuelType = "gasoline" | "diesel" | "electric" | "hybrid";
export type VehicleStatus = "active" | "maintenance" | "retired" | "inactive";

// 证件信息类型
export interface Certificate {
  id: string;
  type: CertificateType;
  number: string;
  issueDate: string;
  expiryDate: string;
  issuer: string;
  status: CertificateStatus;
  attachments: string[];
}

export type CertificateType =
  | "license"
  | "registration"
  | "insurance"
  | "inspection";
export type CertificateStatus = "valid" | "expired" | "expiring" | "suspended";

// 维修记录类型
export interface MaintenanceRecord {
  id: string;
  vehicleId: string;
  type: MaintenanceType;
  description: string;
  cost: number;
  mileage: number;
  maintenanceDate: string;
  nextMaintenanceDate?: string;
  technician: string;
  workshop: string;
  parts: MaintenancePart[];
  status: MaintenanceStatus;
}

export type MaintenanceType = "routine" | "repair" | "emergency" | "inspection";
export type MaintenanceStatus =
  | "completed"
  | "in_progress"
  | "scheduled"
  | "cancelled";

export interface MaintenancePart {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

// 人员相关类型
export interface Personnel {
  id: string;
  employeeNumber: string; // 工号
  name: string; // 姓名
  gender: "male" | "female"; // 性别
  birthDate: string; // 出生日期
  idCard: string; // 身份证号
  phone: string; // 手机号
  email: string; // 邮箱
  address: string; // 地址
  position: string; // 职位
  department: string; // 部门
  hireDate: string; // 入职日期
  status: PersonnelStatus; // 状态
  emergencyContact: EmergencyContact;
  licenses: License[]; // 证件信息
  trainings: TrainingRecord[]; // 培训记录
  violations: ViolationRecord[]; // 违章记录
  createdAt: string;
  updatedAt: string;
}

export type PersonnelStatus = "active" | "inactive" | "resigned" | "suspended";

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
}

// 驾驶证信息
export interface License {
  id: string;
  type: LicenseType;
  number: string;
  class: string; // 准驾车型
  issueDate: string;
  expiryDate: string;
  issuer: string;
  status: LicenseStatus;
  points: number; // 扣分
}

export type LicenseType = "driving" | "qualification" | "health" | "safety";
export type LicenseStatus = "valid" | "expired" | "suspended" | "revoked";

// 培训记录
export interface TrainingRecord {
  id: string;
  personnelId: string;
  courseId: string;
  courseName: string;
  trainingDate: string;
  duration: number; // 培训时长(小时)
  score: number;
  status: TrainingStatus;
  certificate?: string;
}

export type TrainingStatus =
  | "completed"
  | "in_progress"
  | "failed"
  | "cancelled";

// 违章记录
export interface ViolationRecord {
  id: string;
  personnelId: string;
  vehicleId?: string;
  violationType: string;
  description: string;
  violationDate: string;
  location: string;
  fine: number;
  points: number;
  status: ViolationStatus;
}

export type ViolationStatus =
  | "pending"
  | "processed"
  | "appealed"
  | "dismissed";

// 安全检查相关类型
export interface Inspection {
  id: string;
  vehicleId: string;
  inspectorId: string;
  inspectionDate: string;
  inspectionType: InspectionType;
  status: InspectionStatus;
  score: number; // 检查得分
  items: InspectionItem[]; // 检查项目
  hazards: Hazard[]; // 发现隐患
  photos: string[]; // 检查照片
  remarks: string; // 备注
  createdAt: string;
  updatedAt: string;
}

export type InspectionType =
  | "daily"
  | "weekly"
  | "monthly"
  | "special"
  | "random";
export type InspectionStatus =
  | "pending"
  | "in_progress"
  | "completed"
  | "failed";

// 检查项目
export interface InspectionItem {
  id: string;
  category: string; // 检查类别
  item: string; // 检查项目
  standard: string; // 检查标准
  result: InspectionResult; // 检查结果
  description: string; // 检查描述
  photos: string[];
}

export type InspectionResult = "pass" | "fail" | "warning" | "na";

// 隐患记录
export interface Hazard {
  id: string;
  level: HazardLevel;
  category: string;
  description: string;
  location: string;
  discoveryDate: string;
  status: HazardStatus;
  assignee: string; // 负责人
  dueDate: string; // 整改期限
  resolution: string; // 整改措施
  photos: string[];
  completionDate?: string;
}

export type HazardLevel = "low" | "medium" | "high" | "critical";
export type HazardStatus =
  | "open"
  | "in_progress"
  | "resolved"
  | "closed"
  | "overdue";

// 仪表板数据类型
export interface DashboardStats {
  vehicleStats: {
    total: number;
    active: number;
    maintenance: number;
    retired: number;
  };
  personnelStats: {
    total: number;
    drivers: number;
    escorts: number;
    managers: number;
  };
  safetyStats: {
    inspections: number;
    hazards: number;
    violations: number;
    accidents: number;
  };
  trends: {
    inspectionTrend: TrendData[];
    hazardTrend: TrendData[];
    violationTrend: TrendData[];
  };
}

export interface TrendData {
  date: string;
  value: number;
  label?: string;
}

// 表单相关类型
export interface FormRule {
  required?: boolean;
  message?: string;
  trigger?: string | string[];
  validator?: (rule: any, value: any, callback: any) => void;
}

export interface FormRules {
  [key: string]: FormRule[];
}

// 表格相关类型
export interface TableColumn {
  prop: string;
  label: string;
  width?: string | number;
  minWidth?: string | number;
  fixed?: boolean | "left" | "right";
  sortable?: boolean;
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
}

// 菜单相关类型
export interface MenuItem {
  id: string;
  name: string;
  path: string;
  icon?: string;
  children?: MenuItem[];
  meta?: {
    title: string;
    hidden?: boolean;
  };
}

// 通知相关类型
export interface Notification {
  id: string;
  title: string;
  content: string;
  type: "info" | "success" | "warning" | "error";
  read: boolean;
  createdAt: string;
}

// 文件上传类型
export interface UploadFile {
  id: string;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadDate: string;
}
