/*! Element Plus v2.9.11 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleTa = factory());
})(this, (function () { 'use strict';

  var ta = {
    name: "ta",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "\u0B89\u0BB1\u0BC1\u0BA4\u0BBF \u0B9A\u0BC6\u0BAF\u0BCD",
        clear: "\u0BA4\u0BC6\u0BB3\u0BBF\u0BB5\u0BBE\u0B95\u0BCD\u0B95\u0BC1"
      },
      datepicker: {
        now: "\u0BA4\u0BB1\u0BCD\u0BAA\u0BCB\u0BA4\u0BC1",
        today: "\u0B87\u0BA9\u0BCD\u0BB1\u0BC1",
        cancel: "\u0BB0\u0BA4\u0BCD\u0BA4\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD",
        clear: "\u0B9A\u0BB0\u0BBF",
        confirm: "\u0B89\u0BB1\u0BC1\u0BA4\u0BBF \u0B9A\u0BC6\u0BAF\u0BCD",
        selectDate: "\u0BA4\u0BC7\u0BA4\u0BBF\u0BAF\u0BC8 \u0BA4\u0BC7\u0BB0\u0BCD\u0BB5\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD",
        selectTime: "\u0BA8\u0BC7\u0BB0\u0BA4\u0BCD\u0BA4\u0BC8 \u0BA4\u0BC7\u0BB0\u0BCD\u0BB5\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD",
        startDate: "\u0BA4\u0BCA\u0B9F\u0B99\u0BCD\u0B95\u0BC1\u0BAE\u0BCD \u0BA8\u0BBE\u0BB3\u0BCD",
        startTime: "\u0BA4\u0BCA\u0B9F\u0B99\u0BCD\u0B95\u0BC1\u0BAE\u0BCD \u0BA8\u0BC7\u0BB0\u0BAE\u0BCD",
        endDate: "\u0BAE\u0BC1\u0B9F\u0BBF\u0BAF\u0BC1\u0BAE\u0BCD \u0BA4\u0BC7\u0BA4\u0BBF",
        endTime: "\u0BAE\u0BC1\u0B9F\u0BBF\u0BAF\u0BC1\u0BAE\u0BCD \u0BA8\u0BC7\u0BB0\u0BAE\u0BCD",
        prevYear: "Previous Year",
        nextYear: "Next Year",
        prevMonth: "Previous Month",
        nextMonth: "Next Month",
        year: "\u0BB5\u0BB0\u0BC1\u0B9F\u0BAE\u0BCD",
        month1: "\u0B9C\u0BA9\u0BB5\u0BB0\u0BBF",
        month2: "\u0BAA\u0BBF\u0BAA\u0BCD\u0BB0\u0BB5\u0BB0\u0BBF",
        month3: "\u0BAE\u0BBE\u0BB0\u0BCD\u0B9A\u0BCD",
        month4: "\u0B8F\u0BAA\u0BCD\u0BB0\u0BB2\u0BCD",
        month5: "\u0BAE\u0BC7",
        month6: "\u0B9C\u0BC2\u0BA9\u0BCD",
        month7: "\u0B9C\u0BC2\u0BB2\u0BC8",
        month8: "\u0B86\u0B95\u0BB8\u0BCD\u0B9F\u0BCD",
        month9: "\u0B9A\u0BC6\u0BAA\u0BCD\u0B9F\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
        month10: "\u0B85\u0B95\u0BCD\u0B9F\u0BCB\u0BAA\u0BB0\u0BCD",
        month11: "\u0BA8\u0BB5\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
        month12: "\u0B9F\u0BBF\u0B9A\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
        weeks: {
          sun: "\u0B9E\u0BBE\u0BAF\u0BBF\u0BB1\u0BC1",
          mon: "\u0BA4\u0BBF\u0B99\u0BCD\u0B95\u0BB3\u0BCD",
          tue: "\u0B9A\u0BC6\u0BB5\u0BCD\u0BB5\u0BBE\u0BAF\u0BCD",
          wed: "\u0BAA\u0BC1\u0BA4\u0BA9\u0BCD",
          thu: "\u0BB5\u0BBF\u0BAF\u0BBE\u0BB4\u0BA9\u0BCD",
          fri: "\u0BB5\u0BC6\u0BB3\u0BCD\u0BB3\u0BBF",
          sat: "\u0B9A\u0BA9\u0BBF"
        },
        months: {
          jan: "\u0B9C\u0BA9\u0BB5\u0BB0\u0BBF",
          feb: "\u0BAA\u0BBF\u0BAA\u0BCD\u0BB0\u0BB5\u0BB0\u0BBF",
          mar: "\u0BAE\u0BBE\u0BB0\u0BCD\u0B9A\u0BCD",
          apr: "\u0B8F\u0BAA\u0BCD\u0BB0\u0BB2\u0BCD",
          may: "\u0BAE\u0BC7",
          jun: "\u0B9C\u0BC2\u0BA9\u0BCD",
          jul: "\u0B9C\u0BC2\u0BB2\u0BC8",
          aug: "\u0B86\u0B95\u0BB8\u0BCD\u0B9F\u0BCD",
          sep: "\u0B9A\u0BC6\u0BAA\u0BCD\u0B9F\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
          oct: "\u0B85\u0B95\u0BCD\u0B9F\u0BCB\u0BAA\u0BB0\u0BCD",
          nov: "\u0BA8\u0BB5\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD",
          dec: "\u0B9F\u0BBF\u0B9A\u0BAE\u0BCD\u0BAA\u0BB0\u0BCD"
        }
      },
      select: {
        loading: "\u0BA4\u0BAF\u0BBE\u0BB0\u0BBE\u0B95\u0BBF\u0B95\u0BCD\u0B95\u0BCA\u0BA3\u0BCD\u0B9F\u0BBF\u0BB0\u0BC1\u0B95\u0BCD\u0B95\u0BBF\u0BB1\u0BA4\u0BC1",
        noMatch: "\u0BAA\u0BCA\u0BB0\u0BC1\u0BA4\u0BCD\u0BA4\u0BAE\u0BBE\u0BA9 \u0BA4\u0BB0\u0BB5\u0BC1 \u0B95\u0BBF\u0B9F\u0BC8\u0B95\u0BCD\u0B95\u0BB5\u0BBF\u0BB2\u0BCD\u0BB2\u0BC8",
        noData: "\u0BA4\u0BB0\u0BB5\u0BC1 \u0B87\u0BB2\u0BCD\u0BB2\u0BC8",
        placeholder: "\u0BA4\u0BC7\u0BB0\u0BCD\u0BB5\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD"
      },
      mention: {
        loading: "\u0BA4\u0BAF\u0BBE\u0BB0\u0BBE\u0B95\u0BBF\u0B95\u0BCD\u0B95\u0BCA\u0BA3\u0BCD\u0B9F\u0BBF\u0BB0\u0BC1\u0B95\u0BCD\u0B95\u0BBF\u0BB1\u0BA4\u0BC1"
      },
      cascader: {
        noMatch: "\u0BAA\u0BCA\u0BB0\u0BC1\u0BA4\u0BCD\u0BA4\u0BAE\u0BBE\u0BA9 \u0BA4\u0BB0\u0BB5\u0BC1 \u0B95\u0BBF\u0B9F\u0BC8\u0B95\u0BCD\u0B95\u0BB5\u0BBF\u0BB2\u0BCD\u0BB2\u0BC8",
        loading: "\u0BA4\u0BAF\u0BBE\u0BB0\u0BBE\u0B95\u0BBF\u0B95\u0BCD\u0B95\u0BCA\u0BA3\u0BCD\u0B9F\u0BBF\u0BB0\u0BC1\u0B95\u0BCD\u0B95\u0BBF\u0BB1\u0BA4\u0BC1",
        placeholder: "\u0BA4\u0BC7\u0BB0\u0BCD\u0BB5\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD",
        noData: "\u0BA4\u0BB0\u0BB5\u0BC1 \u0B87\u0BB2\u0BCD\u0BB2\u0BC8"
      },
      pagination: {
        goto: "\u0BA4\u0BC7\u0BB5\u0BC8\u0BAF\u0BBE\u0BA9\u0BCD \u0BAA\u0B95\u0BC1\u0BA4\u0BBF\u0B95\u0BCD\u0B95\u0BC1 \u0B9A\u0BC6\u0BB2\u0BCD",
        pagesize: "/page",
        total: "\u0BAE\u0BCA\u0BA4\u0BCD\u0BA4\u0BAE\u0BCD {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "\u0B9A\u0BC6\u0BAF\u0BCD\u0BA4\u0BBF",
        confirm: "\u0B89\u0BB1\u0BC1\u0BA4\u0BBF \u0B9A\u0BC6\u0BAF\u0BCD",
        cancel: "\u0BB0\u0BA4\u0BCD\u0BA4\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD",
        error: "\u0BAA\u0BCA\u0BB0\u0BC1\u0BA4\u0BCD\u0BA4\u0BBE\u0BAE\u0BBF\u0BB2\u0BCD\u0BB2\u0BBE\u0BA4 \u0B89\u0BB3\u0BCD\u0BB3\u0BC0\u0B9F\u0BC1"
      },
      upload: {
        deleteTip: "press delete to remove",
        delete: "\u0BA8\u0BC0\u0B95\u0BCD\u0B95\u0BC1",
        preview: "\u0BAE\u0BC1\u0BA9\u0BCD\u0BA9\u0BCB\u0B9F\u0BCD\u0B9F\u0BAE\u0BCD \u0BAA\u0BBE\u0BB0\u0BCD",
        continue: "\u0BA4\u0BCA\u0B9F\u0BB0\u0BC1"
      },
      table: {
        emptyText: "\u0BA4\u0BB0\u0BB5\u0BC1 \u0B87\u0BB2\u0BCD\u0BB2\u0BC8",
        confirmFilter: "\u0B89\u0BB1\u0BC1\u0BA4\u0BBF \u0B9A\u0BC6\u0BAF\u0BCD",
        resetFilter: "\u0BAA\u0BC1\u0BA4\u0BC1\u0BAE\u0BBE\u0BB1\u0BCD\u0BB1\u0BAE\u0BCD \u0B9A\u0BC6\u0BAF\u0BCD",
        clearFilter: "\u0B85\u0BA9\u0BC8\u0BA4\u0BCD\u0BA4\u0BC1\u0BAE\u0BCD",
        sumText: "\u0B95\u0BC2\u0B9F\u0BCD\u0B9F\u0BC1"
      },
      tree: {
        emptyText: "\u0BA4\u0BB0\u0BB5\u0BC1 \u0B87\u0BB2\u0BCD\u0BB2\u0BC8"
      },
      transfer: {
        noMatch: "\u0BAA\u0BCA\u0BB0\u0BC1\u0BA4\u0BCD\u0BA4\u0BAE\u0BBE\u0BA9 \u0BA4\u0BB0\u0BB5\u0BC1 \u0B95\u0BBF\u0B9F\u0BC8\u0B95\u0BCD\u0B95\u0BB5\u0BBF\u0BB2\u0BCD\u0BB2\u0BC8",
        noData: "\u0BA4\u0BB0\u0BB5\u0BC1 \u0B87\u0BB2\u0BCD\u0BB2\u0BC8",
        titles: ["\u0BAA\u0B9F\u0BCD\u0B9F\u0BBF\u0BAF\u0BB2\u0BCD 1", "\u0BAA\u0B9F\u0BCD\u0B9F\u0BBF\u0BAF\u0BB2\u0BCD 2"],
        filterPlaceholder: "\u0B9A\u0BCA\u0BB2\u0BCD\u0BB2\u0BC8 \u0B89\u0BB3\u0BCD\u0BB3\u0BC0\u0B9F\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD",
        noCheckedFormat: "{total} items",
        hasCheckedFormat: "{checked}/{total} \u0BA4\u0BC7\u0BB0\u0BCD\u0BB5\u0BC1 \u0B9A\u0BC6\u0BAF\u0BCD\u0BAF\u0BAA\u0BCD\u0BAA\u0B9F\u0BCD\u0B9F\u0BB5\u0BC8\u0B95\u0BB3\u0BCD"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return ta;

}));
